<script setup lang="ts">
import { useListingStore } from '#imports'
import type { RadioGroupItem } from '@nuxt/ui'
import { ref, computed } from 'vue'
import {
  getWorkflowFromTemplate,
  addMessageStep,
  deleteLastMessageStep,
} from '~/shared/templates/workflows/utils'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const listingStore = useListingStore()

const toast = useToast()

const workflow = ref<any | null>(null)

// Reactive form data
const formData = ref({
  name: '',
  description: '',
  type: 'Upsell',
  startCondition: 'Before Check-in',
  days: 3,
  active: true,
  sendEmail: true,
  sendSms: true,
  sendOta: true,
  steps: [],
  listingIds: [] as string[],
})

// Options for URadioGroup and USelectMenu
const workflowTypeOptions = ref<RadioGroupItem[]>(['recovery', 'upsell', 'communication'])

const startConditionOptions = [
  'Before Check-in',
  'After Check-in',
  'Before Check-out',
  'When Cart Abandoned',
  'When Inquiry Received',
]

// Listing items for the multi-select
const listingItems = computed(() => [
  { label: 'Select All', value: 'select-all', special: true },
  ...listingStore.listings.map((l: any) => ({ label: l.name, value: l.id })),
])

// Handle listing selection with "Select All" functionality
function handleListingSelection(selectedValues: string[]) {
  if (selectedValues.includes('select-all')) {
    // If "Select All" is selected, select all actual listings
    if (formData.value.listingIds.length === listingStore.listings.length) {
      // If all are already selected, deselect all
      formData.value.listingIds = []
    } else {
      // Select all listings
      formData.value.listingIds = listingStore.listings.map((l: any) => l.id)
    }
  } else {
    // Normal selection without "Select All"
    formData.value.listingIds = selectedValues.filter(v => v !== 'select-all')
  }
}

// START: Added Name Template Options and Function
const nameTemplateOptions = [
  [
    { label: 'Inquiry Winback' },
    { label: 'Early Check In Upsell' },
    { label: 'Late Check Out Upsell' },
    { label: 'Pre Stay Extra Night Upsell' },
    { label: 'Post Stay Extra Night Upsell' },
  ],
  // You can add more groups of items if needed, separated by arrays
  // e.g., [{ label: 'Another Option' }]
]

function handleTemplateSelect(templateName: string) {
  let type: 'winback' | 'upsell' | null = null
  if (templateName.toLowerCase().includes('winback')) type = 'winback'
  if (templateName.toLowerCase().includes('upsell')) type = 'upsell'
  if (!type) return
  workflow.value = getWorkflowFromTemplate(type)
  // Populate formData fields from workflow
  formData.value.name = workflow.value.name
  formData.value.description = workflow.value.description
  formData.value.type = workflow.value.type === 'winback' ? 'Recovery' : 'Upsell'
  formData.value.steps = stepsArrayFromWorkflow(workflow.value)
}

function stepsArrayFromWorkflow(wf: any) {
  // Convert steps object to array with id/type/subject/body/duration
  return Object.entries(wf.steps).map(([id, step]: any) => ({
    id,
    ...step
  }))
}

function syncFormSteps() {
  if (workflow.value) {
    formData.value.steps = stepsArrayFromWorkflow(workflow.value)
  }
}

function handleAddStep() {
  if (workflow.value) {
    addMessageStep(workflow.value)
    syncFormSteps()
  }
}

function handleRemoveStep() {
  if (workflow.value) {
    deleteLastMessageStep(workflow.value)
    syncFormSteps()
  }
}

function handleUpdateStep({ id, field, value }: { id: string, field: string, value: string }) {
  if (!workflow.value) return
  if (workflow.value.steps[id]) {
    workflow.value.steps[id][field] = value
    syncFormSteps()
  }
}
// END: Added Name Template Options and Function

async function handleSave() {
  try {
    await useApiFetch('workflows', {
      method: 'POST',
      body: formData.value,
    })

    toast.add({
      title: 'Success',
      description: 'Workflow created successfully',
      color: 'success',
    })
  }
  catch (error) {
    console.error('Failed to create workflow:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to create workflow',
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel id="workflow-new">
    <template #header>
      <UDashboardNavbar title="Create New Workflow">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="grid w-full gap-6">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Basic Information
            </h3>
          </template>

          <div class="space-y-4">
            <div class="mb-2 flex justify-end">
              <UDropdownMenu :items="nameTemplateOptions" :popper="{ placement: 'bottom-start' }">
                <UButton color="primary" label="Start with Template" trailing-icon="i-heroicons-chevron-down-20-solid" />

                <template #item="{ item }">
                  <span class="truncate" @click="handleTemplateSelect(item.label)">{{ item.label }}</span>
                </template>
              </UDropdownMenu>
            </div>
            <UFormField label="Workflow Name" name="name" required>
              <UInput id="name" v-model="formData.name" class="flex" placeholder="e.g., Early Check-in Offer" />
            </UFormField>

            <UFormField label="Description" name="description">
              <UTextarea id="description" v-model="formData.description" class="flex"
                placeholder="What does this workflow do?" :rows="2" />
            </UFormField>

            <UFormField label="Workflow Type" name="workflowType">
              <URadioGroup v-model="formData.type" :items="workflowTypeOptions"
                class="grid grid-cols-1 gap-3 sm:grid-cols-3" />
            </UFormField>

            <template v-if="formData.type !== 'Recovery'">
              <UFormField label="When should this workflow start?" name="startCondition">
                <USelectMenu v-model="formData.startCondition" :items="startConditionOptions"
                  placeholder="Select when to start" />
              </UFormField>

              <UFormField label="How many days before/after?" name="days">
                <UInput id="days" v-model.number="formData.days" class="flex" type="number" />
              </UFormField>
            </template>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Listings
            </h3>
          </template>

          <div class="space-y-4">
            <UFormField label="Select Listings" name="listingIds">
              <USelectMenu :model-value="formData.listingIds" :items="listingItems" multiple
                placeholder="Choose which listings this workflow applies to" class="w-full max-w-[240px]"
                label-key="label" value-key="value" @update:model-value="handleListingSelection" />
            </UFormField>

            <div v-if="formData.listingIds.length > 0" class="mt-3">
              <p class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Selected Listings ({{ formData.listingIds.length }} of {{ listingStore.listings.length }}):
              </p>
              <div class="flex flex-wrap gap-2">
                <UBadge v-for="listingId in formData.listingIds" :key="listingId" variant="soft" color="primary"
                  size="sm">
                  {{ listingStore.listings.find(l => l.id === listingId)?.name || listingId }}
                </UBadge>
              </div>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Message Sequence
            </h3>
          </template>
          <div>
            <WorkflowsNewSimpleWorkflowBuilder :steps="formData.steps" @add-step="handleAddStep"
              @remove-step="handleRemoveStep" @update-step="handleUpdateStep" />
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Settings
            </h3>
          </template>
          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              <USwitch id="active" v-model="formData.active" />
              <label for="active" class="text-sm font-medium text-gray-900 dark:text-white">Activate workflow
                immediately</label>
            </div>
            <div class="flex items-center space-x-3">
              <USwitch id="email" v-model="formData.sendEmail" />
              <label for="email" class="text-sm font-medium text-gray-900 dark:text-white">Send via email</label>
            </div>
            <div class="flex items-center space-x-3">
              <USwitch id="sms" v-model="formData.sendSms" />
              <label for="sms" class="text-sm font-medium text-gray-900 dark:text-white">Send via SMS</label>
            </div>
          </div>
        </UCard>

        <div class="flex items-center justify-end gap-2">
          <UButton variant="outline" color="neutral">
            Save as Draft
          </UButton>
          <UButton icon="i-heroicons-arrow-down-tray-20-solid" @click="handleSave()">
            Create Workflow
          </UButton>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped></style>
